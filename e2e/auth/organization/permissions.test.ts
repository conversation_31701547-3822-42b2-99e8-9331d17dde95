import { expect, test } from '@playwright/test';

test.describe('Organization Permissions E2E Tests', () => {
	// Generate unique test identifiers
	const timestamp = Date.now();
	const ownerEmail = `owner-${timestamp}@example.com`;
	const adminEmail = `admin-${timestamp}@example.com`;
	const memberEmail = `member-${timestamp}@example.com`;
	const password = 'TestPassword123';
	const orgName = `Test Org ${timestamp}`;

	// State to store generated data
	let orgId = '';

	// Setup: Create owner account and organization
	test.beforeAll(async ({ browser }) => {
		const page = await browser.newPage();

		// Sign up as owner
		await page.goto('/auth/signup');
		await page.fill('input[name="email"]', ownerEmail);
		await page.fill('input[name="password"]', password);
		await page.click('button[type="submit"]');

		// Wait for redirect to org creation or verification page
		await Promise.race([
			page.waitForURL(/\/organizations\/new/, { timeout: 5000 }),
			page.waitForSelector('text=User created successfully', { timeout: 5000 }),
		]);

		// If on success page, sign in
		if (page.url().includes('/auth/')) {
			await page.goto('/auth/signin');
			await page.fill('input[name="email"]', ownerEmail);
			await page.fill('input[name="password"]', password);
			await page.click('button[type="submit"]');
			await page.waitForURL(/\/org\/new/, { timeout: 5000 });
		}

		// Create the organization
		await page.fill('input[name="name"]', orgName);
		await page.click('button[type="submit"]');

		// Wait for redirect to clients page
		await page.waitForURL(/\/org\/.*\/clients/, { timeout: 5000 });

		// Extract org ID from URL if possible (depends on your routing structure)
		// This is an example that assumes the URL contains the org ID in some form
		const urlParts = page.url().split('/');
		for (const part of urlParts) {
			if (part.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
				orgId = part;
				break;
			}
		}

		// If we couldn't get the org ID from URL, try to get it from the page
		if (!orgId) {
			// The specific way to get the org ID will depend on your application
			// This is just an example approach
			await page.goto('/settings');
			const orgIdElement = page.locator('[data-org-id]').first();
			if (await orgIdElement.isVisible()) {
				orgId = (await orgIdElement.getAttribute('data-org-id')) || '';
			}
		}

		await page.close();
	});

	test('owner should have full access to organization settings', async ({ page }) => {
		// Sign in as owner
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', ownerEmail);
		await page.fill('input[name="password"]', password);
		await page.click('button[type="submit"]');

		// Wait for auth to complete
		await page.waitForURL(/\/org\/.*\/clients/, { timeout: 5000 });

		// Navigate to organization settings
		await page.goto(`/org/${encodeURIComponent(orgName)}/settings`);

		// Check for settings only owners should see
		await expect(page.getByText(/delete organization/i)).toBeVisible();
		await expect(page.getByText(/transfer ownership/i)).toBeVisible();
		await expect(page.getByText(/invite members/i)).toBeVisible();

		// Verify organization name is displayed
		await expect(page.getByText(orgName)).toBeVisible();
	});

	test('should allow owner to invite members with different roles', async ({ page }) => {
		// Sign in as owner
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', ownerEmail);
		await page.fill('input[name="password"]', password);
		await page.click('button[type="submit"]');

		// Wait for auth to complete
		await page.waitForURL(/\/(clients|dashboard)/, { timeout: 5000 });

		// Navigate to member invitation page
		// This URL might be different in your application
		await page.goto('/organizations/invite');

		// Fill invitation form for admin role
		await page.fill('input[name="email"]', adminEmail);
		await page.selectOption('select[name="role"]', 'admin');
		await page.click('button[type="submit"]');

		// Verify success
		await expect(page.getByText(/invitation sent|invited successfully/i)).toBeVisible({
			timeout: 5000,
		});

		// Invite a regular member
		await page.fill('input[name="email"]', memberEmail);
		await page.selectOption('select[name="role"]', 'member');
		await page.click('button[type="submit"]');

		// Verify success
		await expect(page.getByText(/invitation sent|invited successfully/i)).toBeVisible({
			timeout: 5000,
		});

		// Navigate to members list
		await page.goto('/organizations/members');

		// Verify invited members are listed
		await expect(page.getByText(adminEmail)).toBeVisible();
		await expect(page.getByText(memberEmail)).toBeVisible();
	});

	// Note: Testing actual invitation acceptance would require email integration
	// The following test simulates a user trying to access resources they don't have permission for

	test('should enforce permission boundaries between organizations', async ({ browser }) => {
		// Create a new user that doesn't belong to the test organization
		const outsiderPage = await browser.newPage();
		const outsiderEmail = `outsider-${timestamp}@example.com`;

		// Sign up as outsider
		await outsiderPage.goto('/auth/signup');
		await outsiderPage.fill('input[name="email"]', outsiderEmail);
		await outsiderPage.fill('input[name="password"]', password);
		await outsiderPage.click('button[type="submit"]');

		// Complete signup flow
		await Promise.race([
			outsiderPage.waitForURL(/\/organizations\/new/, { timeout: 5000 }),
			outsiderPage.waitForSelector('text=User created successfully', { timeout: 5000 }),
		]);

		// If on success page, sign in
		if (outsiderPage.url().includes('/auth/')) {
			await outsiderPage.goto('/auth/signin');
			await outsiderPage.fill('input[name="email"]', outsiderEmail);
			await outsiderPage.fill('input[name="password"]', password);
			await outsiderPage.click('button[type="submit"]');
			await outsiderPage.waitForURL(/\/organizations\/new/, { timeout: 5000 });
		}

		// Create their own organization
		await outsiderPage.fill('input[name="name"]', `Outsider Org ${timestamp}`);
		await outsiderPage.click('button[type="submit"]');

		// Wait for redirect to dashboard/clients
		await outsiderPage.waitForURL(/\/(clients|dashboard)/, { timeout: 5000 });

		// Try to access the test organization's data
		// This assumes you have a URL structure that includes organization ID
		if (orgId) {
			await outsiderPage.goto(`/organizations/${orgId}/settings`);

			// Should be redirected to an error page or access denied
			await expect(outsiderPage.url()).not.toContain(`/organizations/${orgId}/settings`);

			// Or should see an access denied message
			const accessDenied = outsiderPage.getByText(
				/access denied|not authorized|forbidden|permission/i,
			);
			const notFound = outsiderPage.getByText(/not found|404|doesn't exist/i);

			// Either the page should not be found or access should be explicitly denied
			await expect(accessDenied.isVisible() || notFound.isVisible()).toBeTruthy();
		}

		await outsiderPage.close();
	});
});
