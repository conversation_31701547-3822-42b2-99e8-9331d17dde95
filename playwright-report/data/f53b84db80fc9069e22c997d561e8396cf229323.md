# Test info

- Name: Authentication E2E Tests >> initiate password reset flow
- Location: /Users/<USER>/Projects/project-controls/e2e/auth/signup-login.test.ts:66:2

# Error details

```
Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

Locator: locator('[data-sonner-toast]')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 10000ms
  - waiting for locator('[data-sonner-toast]')

    at /Users/<USER>/Projects/project-controls/e2e/auth/signup-login.test.ts:80:53
```

# Page snapshot

```yaml
- region "Notifications alt+T"
- link "Cost Atlas":
  - /url: /
  - img
  - text: Cost Atlas
- heading "Reset Password" [level=1]
- text: Email *
- textbox "Email *"
- button "Send Reset Link"
```

# Test source

```ts
   1 | import { expect, test } from '@playwright/test';
   2 |
   3 | // Test fixtures for authentication flows
   4 | test.describe('Authentication E2E Tests', () => {
   5 | 	// Generate a unique email for testing to avoid conflicts
   6 | 	const testEmail = `test-${Date.now()}@example.com`;
   7 | 	const password = 'TestPassword123';
   8 |
   9 | 	test('complete user registration journey', async ({ page }) => {
  10 | 		// Navigate to signup page
  11 | 		await page.goto('/auth/signup');
  12 |
  13 | 		// Verify the page title
  14 | 		await expect(page.locator('h1:has-text("Sign up")')).toBeVisible();
  15 |
  16 | 		// Fill the signup form
  17 | 		await page.fill('input[name="email"]', testEmail);
  18 | 		await page.fill('input[name="password"]', password);
  19 |
  20 | 		// Submit the form
  21 | 		await page.click('button[type="submit"]');
  22 |
  23 | 		// Should be directed to success page or verification notification
  24 | 		await expect(page.locator('text=User created successfully')).toBeVisible({ timeout: 10000 });
  25 | 	});
  26 |
  27 | 	test('failed login with incorrect credentials', async ({ page }) => {
  28 | 		// Navigate to signin page
  29 | 		await page.goto('/auth/signin');
  30 |
  31 | 		// Verify the page title
  32 | 		await expect(page.locator('h1:has-text("Sign in")')).toBeVisible();
  33 |
  34 | 		// Fill the login form with wrong password
  35 | 		await page.fill('input[name="email"]', testEmail);
  36 | 		await page.fill('input[name="password"]', 'WrongPassword123');
  37 |
  38 | 		// Submit the form
  39 | 		await page.click('button[type="submit"]');
  40 |
  41 | 		// Should show an error message
  42 | 		await expect(page.locator('text=Invalid login credentials')).toBeVisible({ timeout: 10000 });
  43 | 	});
  44 |
  45 | 	test('successful login with valid credentials', async ({ page }) => {
  46 | 		// This test depends on the user being already created in the database
  47 | 		// and having verified their email if required by the app
  48 |
  49 | 		// Navigate to signin page
  50 | 		await page.goto('/auth/signin');
  51 |
  52 | 		// Fill the login form with correct credentials
  53 | 		await page.fill('input[name="email"]', testEmail);
  54 | 		await page.fill('input[name="password"]', password);
  55 |
  56 | 		// Submit the form
  57 | 		await page.click('button[type="submit"]');
  58 |
  59 | 		// Verify successful login - should be redirected to dashboard or home
  60 | 		// This may need adjustment based on your app's actual flow
  61 | 		await expect(page).toHaveURL(/\/(dashboard|organizations\/new)/, { timeout: 10000 });
  62 | 	});
  63 |
  64 | 	// Password reset flow test - note this requires email integration
  65 | 	// and can be challenging to fully automate
  66 | 	test('initiate password reset flow', async ({ page }) => {
  67 | 		// Navigate to password reset page
  68 | 		await page.goto('/auth/reset-password');
  69 |
  70 | 		// Verify the page title - should be "Reset Password" (exact match)
  71 | 		await expect(page.locator('h1')).toContainText('Reset Password');
  72 |
  73 | 		// Fill in the email
  74 | 		await page.fill('input[name="email"]', testEmail);
  75 |
  76 | 		// Submit the form
  77 | 		await page.click('button[type="submit"]');
  78 |
  79 | 		// Verify success message via toast
> 80 | 		await expect(page.locator('[data-sonner-toast]')).toBeVisible({ timeout: 10000 });
     | 		                                                  ^ Error: Timed out 10000ms waiting for expect(locator).toBeVisible()
  81 | 		await expect(page.locator('text=/check your email/i')).toBeVisible({ timeout: 10000 });
  82 | 	});
  83 | });
  84 |
```