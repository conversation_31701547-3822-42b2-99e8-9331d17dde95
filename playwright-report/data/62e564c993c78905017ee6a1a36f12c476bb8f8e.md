# Test info

- Name: Organization member invitation >> should validate form inputs
- Location: /Users/<USER>/Projects/project-controls/e2e/organizations/invite-member.test.ts:34:2

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "**/api/org-selection" until "load"
  navigated to "http://localhost:5173/auth/signin"
============================================================
    at /Users/<USER>/Projects/project-controls/e2e/organizations/invite-member.test.ts:12:14
```

# Page snapshot

```yaml
- region "Notifications alt+T"
- link "Cost Atlas":
  - /url: /
  - img
  - text: Cost Atlas
- heading "Sign In" [level=1]
- text: Email *
- textbox "Email *": <EMAIL>
- text: Password *
- textbox "Password *": password123
- text: Invalid login credentials
- button "Sign In"
- link "Forgot your password?":
  - /url: /auth/reset-password
- link "Need an account?":
  - /url: /auth/signup
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Organization member invitation', () => {
   4 | 	test.beforeEach(async ({ page }) => {
   5 | 		// Go to the homepage and login
   6 | 		await page.goto('/auth/signin');
   7 | 		await page.fill('input[name="email"]', '<EMAIL>');
   8 | 		await page.fill('input[name="password"]', 'password123');
   9 | 		await page.click('button[type="submit"]');
   10 |
   11 | 		// Wait for dashboard to load
>  12 | 		await page.waitForURL('**/api/org-selection');
      | 		           ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
   13 |
   14 | 		// Select the first organization (assuming test user has access)
   15 | 		await page.click('a[href*="/organizations/"]');
   16 |
   17 | 		// Go to members page and then to invite page
   18 | 		await page.click('a[href*="/members"]');
   19 | 		await page.click('a[href*="/members/new"]');
   20 |
   21 | 		// Make sure we're on the invite page
   22 | 		await expect(page).toHaveURL(/\/organizations\/.*\/members\/new/);
   23 | 	});
   24 |
   25 | 	test('should display the invite form', async ({ page }) => {
   26 | 		// Check form elements are present
   27 | 		await expect(page.locator('h1')).toContainText('Invite New Member');
   28 | 		await expect(page.locator('form')).toBeVisible();
   29 | 		await expect(page.locator('input[type="email"]')).toBeVisible();
   30 | 		await expect(page.locator('button:has-text("Send Invitation")')).toBeVisible();
   31 | 		await expect(page.locator('button:has-text("Cancel")')).toBeVisible();
   32 | 	});
   33 |
   34 | 	test('should validate form inputs', async ({ page }) => {
   35 | 		// Try to submit with empty form
   36 | 		await page.click('button:has-text("Send Invitation")');
   37 |
   38 | 		// Expect validation errors
   39 | 		await expect(page.locator('text=Email is required')).toBeVisible();
   40 | 		await expect(page.locator('text=Role is required')).toBeVisible();
   41 |
   42 | 		// Fill in invalid email
   43 | 		await page.fill('input[type="email"]', 'invalid-email');
   44 | 		await page.click('button:has-text("Send Invitation")');
   45 |
   46 | 		// Expect email validation error
   47 | 		await expect(page.locator('text=Invalid email address')).toBeVisible();
   48 | 	});
   49 |
   50 | 	test('should navigate back to members page on cancel', async ({ page }) => {
   51 | 		// Click the cancel button
   52 | 		await page.click('button:has-text("Cancel")');
   53 |
   54 | 		// Expect to be redirected to members page
   55 | 		await expect(page).toHaveURL(/\/organizations\/.*\/members$/);
   56 | 	});
   57 |
   58 | 	test('should successfully invite a new member', async ({ page }) => {
   59 | 		// Fill the form with valid data
   60 | 		await page.fill('input[type="email"]', '<EMAIL>');
   61 |
   62 | 		// Click on the role dropdown and select 'Member'
   63 | 		await page.click('button:has-text("Select a role")');
   64 | 		await page.click('div[role="option"]:has-text("Member")');
   65 |
   66 | 		// Submit the form
   67 | 		await page.click('button:has-text("Send Invitation")');
   68 |
   69 | 		// Expect to be redirected to members page after successful submission
   70 | 		await expect(page).toHaveURL(/\/organizations\/.*\/members$/);
   71 |
   72 | 		// Expect success message or indication
   73 | 		await expect(page.locator('text=Invitation sent')).toBeVisible();
   74 | 	});
   75 |
   76 | 	test('should show error if inviting existing member', async ({ page }) => {
   77 | 		// Fill the form with an email of existing member
   78 | 		await page.fill('input[type="email"]', '<EMAIL>');
   79 |
   80 | 		// Click on the role dropdown and select 'Member'
   81 | 		await page.click('button:has-text("Select a role")');
   82 | 		await page.click('div[role="option"]:has-text("Member")');
   83 |
   84 | 		// Submit the form
   85 | 		await page.click('button:has-text("Send Invitation")');
   86 |
   87 | 		// Expect error message
   88 | 		await expect(page.locator('text=User is already a member')).toBeVisible();
   89 | 	});
   90 |
   91 | 	test('should allow admin role selection', async ({ page }) => {
   92 | 		// Fill the form with valid data
   93 | 		await page.fill('input[type="email"]', '<EMAIL>');
   94 |
   95 | 		// Click on the role dropdown and select 'Admin'
   96 | 		await page.click('button:has-text("Select a role")');
   97 | 		await page.click('div[role="option"]:has-text("Admin")');
   98 |
   99 | 		// Submit the form
  100 | 		await page.click('button:has-text("Send Invitation")');
  101 |
  102 | 		// Expect to be redirected to members page after successful submission
  103 | 		await expect(page).toHaveURL(/\/organizations\/.*\/members$/);
  104 | 	});
  105 | });
  106 |
```