# Test info

- Name: Organization Permissions E2E Tests >> owner should have full access to organization settings
- Location: /Users/<USER>/Projects/project-controls/e2e/auth/organization/permissions.test.ts:71:2

# Error details

```
TimeoutError: page.waitForURL: Timeout 5000ms exceeded.
=========================== logs ===========================
waiting for navigation until "load"
  navigated to "http://localhost:5173/org/new"
============================================================
    at /Users/<USER>/Projects/project-controls/e2e/auth/organization/permissions.test.ts:37:15
```

# Page snapshot

```yaml
- region "Notifications alt+T"
- link:
  - /url: /
  - img
- separator
- list:
  - listitem:
    - link "Clients":
      - /url: /org/new
      - button "Clients":
        - img
        - text: Clients
    - button:
      - img
  - listitem:
    - link "WBS Libraries":
      - /url: /wbs-libraries
      - button "WBS Libraries":
        - img
        - text: WBS Libraries
    - button:
      - img
  - listitem:
    - link "Contractors":
      - /url: /contractors
      - button "Contractors":
        - img
        - text: Contractors
    - button:
      - img
- separator
- list:
  - listitem:
    - button "U"
- button "Toggle Sidebar"
- main:
  - button "Toggle Sidebar":
    - img
    - text: Toggle Sidebar
  - heading "Create Organization" [level=1]
  - text: Name *
  - textbox "Name *"
  - text: Description
  - textbox "Description"
  - text: Logo URL
  - textbox "Logo URL"
  - button "Create Organization"
```

# Test source

```ts
   1 | import { expect, test } from '@playwright/test';
   2 |
   3 | test.describe('Organization Permissions E2E Tests', () => {
   4 | 	// Generate unique test identifiers
   5 | 	const timestamp = Date.now();
   6 | 	const ownerEmail = `owner-${timestamp}@example.com`;
   7 | 	const adminEmail = `admin-${timestamp}@example.com`;
   8 | 	const memberEmail = `member-${timestamp}@example.com`;
   9 | 	const password = 'TestPassword123';
   10 | 	const orgName = `Test Org ${timestamp}`;
   11 |
   12 | 	// State to store generated data
   13 | 	let orgId = '';
   14 |
   15 | 	// Setup: Create owner account and organization
   16 | 	test.beforeAll(async ({ browser }) => {
   17 | 		const page = await browser.newPage();
   18 |
   19 | 		// Sign up as owner
   20 | 		await page.goto('/auth/signup');
   21 | 		await page.fill('input[name="email"]', ownerEmail);
   22 | 		await page.fill('input[name="password"]', password);
   23 | 		await page.click('button[type="submit"]');
   24 |
   25 | 		// Wait for redirect to org creation or verification page
   26 | 		await Promise.race([
   27 | 			page.waitForURL(/\/organizations\/new/, { timeout: 5000 }),
   28 | 			page.waitForSelector('text=User created successfully', { timeout: 5000 }),
   29 | 		]);
   30 |
   31 | 		// If on success page, sign in
   32 | 		if (page.url().includes('/auth/')) {
   33 | 			await page.goto('/auth/signin');
   34 | 			await page.fill('input[name="email"]', ownerEmail);
   35 | 			await page.fill('input[name="password"]', password);
   36 | 			await page.click('button[type="submit"]');
>  37 | 			await page.waitForURL(/\/organizations\/new/, { timeout: 5000 });
      | 			           ^ TimeoutError: page.waitForURL: Timeout 5000ms exceeded.
   38 | 		}
   39 |
   40 | 		// Create the organization
   41 | 		await page.fill('input[name="name"]', orgName);
   42 | 		await page.click('button[type="submit"]');
   43 |
   44 | 		// Wait for redirect to dashboard/clients
   45 | 		await page.waitForURL(/\/(clients|dashboard)/, { timeout: 5000 });
   46 |
   47 | 		// Extract org ID from URL if possible (depends on your routing structure)
   48 | 		// This is an example that assumes the URL contains the org ID in some form
   49 | 		const urlParts = page.url().split('/');
   50 | 		for (const part of urlParts) {
   51 | 			if (part.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
   52 | 				orgId = part;
   53 | 				break;
   54 | 			}
   55 | 		}
   56 |
   57 | 		// If we couldn't get the org ID from URL, try to get it from the page
   58 | 		if (!orgId) {
   59 | 			// The specific way to get the org ID will depend on your application
   60 | 			// This is just an example approach
   61 | 			await page.goto('/settings');
   62 | 			const orgIdElement = page.locator('[data-org-id]').first();
   63 | 			if (await orgIdElement.isVisible()) {
   64 | 				orgId = (await orgIdElement.getAttribute('data-org-id')) || '';
   65 | 			}
   66 | 		}
   67 |
   68 | 		await page.close();
   69 | 	});
   70 |
   71 | 	test('owner should have full access to organization settings', async ({ page }) => {
   72 | 		// Sign in as owner
   73 | 		await page.goto('/auth/signin');
   74 | 		await page.fill('input[name="email"]', ownerEmail);
   75 | 		await page.fill('input[name="password"]', password);
   76 | 		await page.click('button[type="submit"]');
   77 |
   78 | 		// Wait for auth to complete
   79 | 		await page.waitForURL(/\/(clients|dashboard)/, { timeout: 5000 });
   80 |
   81 | 		// Navigate to organization settings
   82 | 		await page.goto('/organizations/settings');
   83 |
   84 | 		// Check for settings only owners should see
   85 | 		await expect(page.getByText(/delete organization/i)).toBeVisible();
   86 | 		await expect(page.getByText(/transfer ownership/i)).toBeVisible();
   87 | 		await expect(page.getByText(/invite members/i)).toBeVisible();
   88 |
   89 | 		// Verify organization name is displayed
   90 | 		await expect(page.getByText(orgName)).toBeVisible();
   91 | 	});
   92 |
   93 | 	test('should allow owner to invite members with different roles', async ({ page }) => {
   94 | 		// Sign in as owner
   95 | 		await page.goto('/auth/signin');
   96 | 		await page.fill('input[name="email"]', ownerEmail);
   97 | 		await page.fill('input[name="password"]', password);
   98 | 		await page.click('button[type="submit"]');
   99 |
  100 | 		// Wait for auth to complete
  101 | 		await page.waitForURL(/\/(clients|dashboard)/, { timeout: 5000 });
  102 |
  103 | 		// Navigate to member invitation page
  104 | 		// This URL might be different in your application
  105 | 		await page.goto('/organizations/invite');
  106 |
  107 | 		// Fill invitation form for admin role
  108 | 		await page.fill('input[name="email"]', adminEmail);
  109 | 		await page.selectOption('select[name="role"]', 'admin');
  110 | 		await page.click('button[type="submit"]');
  111 |
  112 | 		// Verify success
  113 | 		await expect(page.getByText(/invitation sent|invited successfully/i)).toBeVisible({
  114 | 			timeout: 5000,
  115 | 		});
  116 |
  117 | 		// Invite a regular member
  118 | 		await page.fill('input[name="email"]', memberEmail);
  119 | 		await page.selectOption('select[name="role"]', 'member');
  120 | 		await page.click('button[type="submit"]');
  121 |
  122 | 		// Verify success
  123 | 		await expect(page.getByText(/invitation sent|invited successfully/i)).toBeVisible({
  124 | 			timeout: 5000,
  125 | 		});
  126 |
  127 | 		// Navigate to members list
  128 | 		await page.goto('/organizations/members');
  129 |
  130 | 		// Verify invited members are listed
  131 | 		await expect(page.getByText(adminEmail)).toBeVisible();
  132 | 		await expect(page.getByText(memberEmail)).toBeVisible();
  133 | 	});
  134 |
  135 | 	// Note: Testing actual invitation acceptance would require email integration
  136 | 	// The following test simulates a user trying to access resources they don't have permission for
  137 |
```