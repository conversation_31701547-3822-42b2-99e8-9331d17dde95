# Test info

- Name: Auth Edge Cases E2E Tests >> should handle network disruptions during auth
- Location: /Users/<USER>/Projects/project-controls/e2e/auth/edge-cases.test.ts:160:2

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: getByText(/network|connection|offline|internet/i)
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for getByText(/network|connection|offline|internet/i)

    at /Users/<USER>/Projects/project-controls/e2e/auth/edge-cases.test.ts:174:72
```

# Test source

```ts
   74 | 			}
   75 | 		} finally {
   76 | 			// Clean up contexts
   77 | 			await Promise.all(contexts.map((context) => context.close()));
   78 | 		}
   79 | 	});
   80 |
   81 | 	test('should handle invalid auth tokens gracefully', async ({ page }) => {
   82 | 		// This test simulates an invalid auth token situation
   83 |
   84 | 		// First, login normally
   85 | 		await page.goto('/auth/signin');
   86 | 		await page.fill('input[name="email"]', testEmail);
   87 | 		await page.fill('input[name="password"]', password);
   88 | 		await page.click('button[type="submit"]');
   89 |
   90 | 		// Wait for successful login
   91 | 		await page.waitForURL(/^(\/|\/auth\/invite\/.*|\/org\/new)$/, { timeout: 5000 });
   92 |
   93 | 		// Now simulate an invalid token by clearing cookies but keeping localStorage
   94 | 		// (this creates a situation where the frontend thinks it's logged in but the token is invalid)
   95 | 		await page.context().clearCookies();
   96 |
   97 | 		// Now navigate to a protected route
   98 | 		await page.goto('/org/new');
   99 |
  100 | 		// The app should gracefully handle this by either:
  101 | 		// 1. Redirecting to login page
  102 | 		// 2. Showing an auth error message
  103 | 		// 3. Attempting to refresh the token and then falling back to login
  104 |
  105 | 		// Check if redirected to login
  106 | 		const isRedirectedToLogin = page.url().includes('/auth/signin');
  107 |
  108 | 		// Or check if an auth error message is shown
  109 | 		const hasAuthError = await page
  110 | 			.getByText(/sign in|login|unauthorized|session expired/i)
  111 | 			.isVisible();
  112 |
  113 | 		// Either condition indicates graceful handling
  114 | 		expect(isRedirectedToLogin || hasAuthError).toBeTruthy();
  115 | 	});
  116 |
  117 | 	test('should prevent access with malformed credentials', async ({ page }) => {
  118 | 		// Test with various malformed inputs
  119 | 		const malformedInputs = [
  120 | 			{ email: "' OR 1=1 --", password: 'password' }, // SQL injection attempt
  121 | 			{ email: '<script>alert(1)</script>@example.com', password: 'password' }, // XSS attempt
  122 | 			{ email: '<EMAIL>', password: "' OR 1=1 --" }, // SQL injection in password
  123 | 			{ email: "user@' UNION SELECT 1,2,3 --", password: 'password' }, // Union injection attempt
  124 | 			{ email: '\<EMAIL>', password: 'password' }, // Null byte injection
  125 | 		];
  126 |
  127 | 		for (const input of malformedInputs) {
  128 | 			await page.goto('/auth/signin');
  129 |
  130 | 			// Fill with malformed input
  131 | 			await page.fill('input[name="email"]', input.email);
  132 | 			await page.fill('input[name="password"]', input.password);
  133 | 			await page.click('button[type="submit"]');
  134 |
  135 | 			// The application should either:
  136 | 			// 1. Show a validation error
  137 | 			// 2. Show a login failure message
  138 | 			// 3. Not redirect to authenticated pages
  139 |
  140 | 			// Check for validation error or login failure message
  141 | 			const hasError = await Promise.race([
  142 | 				page
  143 | 					.getByText(/invalid|failed|incorrect|error/i)
  144 | 					.isVisible({ timeout: 3000 })
  145 | 					.catch(() => false),
  146 | 				page
  147 | 					.waitForURL(/\/auth\//, { timeout: 3000 })
  148 | 					.then(() => true)
  149 | 					.catch(() => false),
  150 | 			]);
  151 |
  152 | 			// We should not be redirected to authenticated pages
  153 | 			const isAuthenticated = page.url().includes('/org') || page.url().includes('/wbs-libraries');
  154 |
  155 | 			expect(hasError).toBeTruthy();
  156 | 			expect(isAuthenticated).toBeFalsy();
  157 | 		}
  158 | 	});
  159 |
  160 | 	test('should handle network disruptions during auth', async ({ page }) => {
  161 | 		await page.goto('/auth/signin');
  162 |
  163 | 		// Fill in credentials
  164 | 		await page.fill('input[name="email"]', testEmail);
  165 | 		await page.fill('input[name="password"]', password);
  166 |
  167 | 		// Setup offline mode before submitting
  168 | 		await page.context().setOffline(true);
  169 |
  170 | 		// Submit the form
  171 | 		await page.click('button[type="submit"]');
  172 |
  173 | 		// Application should show a network error
> 174 | 		await expect(page.getByText(/network|connection|offline|internet/i)).toBeVisible({
      | 		                                                                     ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  175 | 			timeout: 5000,
  176 | 		});
  177 |
  178 | 		// Return to online mode
  179 | 		await page.context().setOffline(false);
  180 |
  181 | 		// Try again - this time it should work
  182 | 		await page.click('button[type="submit"]');
  183 |
  184 | 		// Wait for successful login
  185 | 		await Promise.race([
  186 | 			page.waitForURL(/^(\/|\/auth\/invite\/.*|\/org\/new)$/, { timeout: 5000 }),
  187 | 			page.waitForSelector('text=/welcome|success/', { timeout: 5000 }),
  188 | 		]);
  189 | 	});
  190 | });
  191 |
```