# Test info

- Name: Organization Auth E2E Tests >> switch between organizations
- Location: /Users/<USER>/Projects/project-controls/e2e/auth/organization.test.ts:106:2

# Error details

```
TimeoutError: page.click: Timeout 10000ms exceeded.
Call log:
  - waiting for locator('[aria-label="Switch organization"], [data-testid="org-switcher"]')

    at /Users/<USER>/Projects/project-controls/e2e/auth/organization.test.ts:135:14
```

# Page snapshot

```yaml
- region "Notifications alt+T"
- link:
  - /url: /
  - img
- separator
- list:
  - listitem:
    - link "Clients":
      - /url: /org/new
      - button "Clients":
        - img
        - text: Clients
    - button:
      - img
  - listitem:
    - link "WBS Libraries":
      - /url: /wbs-libraries
      - button "WBS Libraries":
        - img
        - text: WBS Libraries
    - button:
      - img
  - listitem:
    - link "Contractors":
      - /url: /contractors
      - button "Contractors":
        - img
        - text: Contractors
    - button:
      - img
- separator
- list:
  - listitem:
    - button "U"
- button "Toggle Sidebar"
- main:
  - button "Toggle Sidebar":
    - img
    - text: Toggle Sidebar
  - separator
  - navigation "breadcrumb":
    - list:
      - listitem:
        - link "Second Org 1748368127401":
          - /url: /org/Second Org 1748368127401
  - heading "Create a Client" [level=1]
  - text: Client Name *
  - textbox "Client Name *"
  - text: Website URL
  - textbox "Website URL"
  - text: Link to Teams Site
  - textbox "Link to Teams Site"
  - text: URL to Teams or other internal client document portal Internal URL Description
  - textbox "Internal URL Description"
  - text: A short description of what the internal URL links to Description
  - textbox "Description"
  - text: Logo by URL
  - textbox "https://example.com/logo.png"
  - button "Submit"
- text: Cost Atlas
```

# Test source

```ts
   35 | 			await page.waitForURL(/\//, { timeout: 10000 });
   36 | 		}
   37 |
   38 | 		await page.close();
   39 | 	});
   40 |
   41 | 	test('create organization and verify in database', async ({ page }) => {
   42 | 		// Sign in
   43 | 		await page.goto('/auth/signin');
   44 | 		await page.fill('input[name="email"]', testEmail);
   45 | 		await page.fill('input[name="password"]', testPassword);
   46 | 		await page.click('button[type="submit"]');
   47 |
   48 | 		await page.waitForURL(/\//, { timeout: 3000 });
   49 | 		await page.goto('/org/new', { timeout: 3000 });
   50 |
   51 | 		// Fill organization details
   52 | 		await page.fill('input[name="name"]', testOrgName);
   53 |
   54 | 		// Optional fields if they exist
   55 | 		try {
   56 | 			await page.fill('textarea[name="description"]', 'Test organization description');
   57 | 		} catch (e) {
   58 | 			// Description field might be optional or not shown
   59 | 		}
   60 |
   61 | 		// Submit the form
   62 | 		await page.click('button[type="submit"]');
   63 |
   64 | 		// Should be redirected to the clients page or dashboard
   65 | 		await expect(page).toHaveURL(/clients\/new/, { timeout: 10000 });
   66 |
   67 | 		// Verify organization name appears in the interface
   68 | 		await expect(page.locator(`text=${testOrgName}`)).toBeVisible({ timeout: 5000 });
   69 | 	});
   70 |
   71 | 	test('invite members with different roles', async ({ page }) => {
   72 | 		// Sign in if not already signed in
   73 | 		if (!page.url().includes('/client')) {
   74 | 			await page.goto('/auth/signin');
   75 | 			await page.fill('input[name="email"]', testEmail);
   76 | 			await page.fill('input[name="password"]', testPassword);
   77 | 			await page.click('button[type="submit"]');
   78 | 			await page.waitForURL(/\/org\/${testOrgName}\/(clients)/, { timeout: 10000 });
   79 | 		}
   80 |
   81 | 		// Go to organization members or settings page
   82 | 		// The actual URL path will depend on your application structure
   83 | 		await page.goto(`/org/${encodeURIComponent(testOrgName)}/members`);
   84 |
   85 | 		// Look for an invite button
   86 | 		await page.click('button, a', { hasText: /invite|add member/i });
   87 |
   88 | 		// Fill the invite form
   89 | 		const inviteEmail = `invite-${Date.now()}@example.com`;
   90 | 		await page.fill('input[name="email"]', inviteEmail);
   91 |
   92 | 		// Select role (assuming there's a select element for roles)
   93 | 		await page.selectOption('select[name="role"]', 'member');
   94 |
   95 | 		// Submit the invitation
   96 | 		await page.click('button[type="submit"]');
   97 |
   98 | 		// Verify success message
   99 | 		await expect(page.locator('text=/invited|invitation sent/')).toBeVisible({ timeout: 5000 });
  100 |
  101 | 		// Verify member appears in members list (might need to refresh)
  102 | 		await page.reload();
  103 | 		await expect(page.locator(`text=${inviteEmail}`)).toBeVisible({ timeout: 5000 });
  104 | 	});
  105 |
  106 | 	test('switch between organizations', async ({ page, browser }) => {
  107 | 		// This test assumes the user has already created one organization
  108 |
  109 | 		// Sign in
  110 | 		await page.goto('/auth/signin');
  111 | 		await page.fill('input[name="email"]', testEmail);
  112 | 		await page.fill('input[name="password"]', testPassword);
  113 | 		await page.click('button[type="submit"]');
  114 |
  115 | 		// Create a second organization
  116 | 		const secondOrgName = `Second Org ${Date.now()}`;
  117 |
  118 | 		// Navigate to create new organization
  119 | 		await page.goto('/org/new');
  120 |
  121 | 		// Fill organization details
  122 | 		await page.fill('input[name="name"]', secondOrgName);
  123 |
  124 | 		// Submit the form
  125 | 		await page.click('button[type="submit"]');
  126 |
  127 | 		// Should be redirected to the clients page or dashboard
  128 | 		await expect(page).toHaveURL(/\/(clients|dashboard)/, { timeout: 10000 });
  129 |
  130 | 		// Now test organization switching
  131 | 		// Note: The exact UI for organization switching will depend on your application
  132 | 		// This is a generic example, adjust selectors based on your implementation
  133 |
  134 | 		// Open organization switcher (often in the sidebar or navbar)
> 135 | 		await page.click('[aria-label="Switch organization"], [data-testid="org-switcher"]');
      | 		           ^ TimeoutError: page.click: Timeout 10000ms exceeded.
  136 |
  137 | 		// Select the first organization we created
  138 | 		await page.click(`text=${testOrgName}`);
  139 |
  140 | 		// Verify the organization switch by checking for organization name in UI
  141 | 		await expect(page.locator(`text=${testOrgName}`)).toBeVisible({ timeout: 5000 });
  142 | 	});
  143 |
  144 | 	test('enforce permission boundaries between orgs', async ({ page, browser }) => {
  145 | 		// This test checks that a user can't access resources from another organization
  146 | 		// We'll simulate this by generating a valid but wrong org ID in the URL
  147 |
  148 | 		// Sign in
  149 | 		await page.goto('/auth/signin');
  150 | 		await page.fill('input[name="email"]', testEmail);
  151 | 		await page.fill('input[name="password"]', testPassword);
  152 | 		await page.click('button[type="submit"]');
  153 |
  154 | 		// Wait for successful login
  155 | 		await expect(page).toHaveURL(/\/org\/{testOrgName}(clients)/, { timeout: 10000 });
  156 |
  157 | 		// Try to access a resource with a made-up organization name
  158 | 		// This should fail or redirect to an error page
  159 | 		const fakeOrgName = 'non-existent-org';
  160 | 		await page.goto(`/org/${fakeOrgName}/settings`);
  161 |
  162 | 		// Verify we get an error or redirect
  163 | 		// This could be a not found page, access denied message, or redirect to valid org
  164 | 		await expect(page).toHaveURL(/\/(not-found|access-denied|org|auth)/, {
  165 | 			timeout: 10000,
  166 | 		});
  167 |
  168 | 		// We should not see any sensitive data from other organizations
  169 | 		await expect(
  170 | 			page.locator('text=API Keys, Billing Information, Payment Methods'),
  171 | 		).not.toBeVisible();
  172 | 	});
  173 | });
  174 |
```