# Test info

- Name: Password Reset E2E Tests >> should validate password requirements
- Location: /Users/<USER>/Projects/project-controls/e2e/auth/password-reset.test.ts:76:2

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('[data-fs-field-errors]') resolved to 2 elements:
    1) <div data-fs-error="" id="formsnap-417" aria-live="assertive" data-fs-field-errors="" class="text-destructive text-sm font-medium">…</div> aka locator('#formsnap-417')
    2) <div data-fs-error="" id="formsnap-422" aria-live="assertive" data-fs-field-errors="" class="text-destructive text-sm font-medium">…</div> aka locator('#formsnap-422')

Call log:
  - expect.toBeVisible with timeout 2000ms
  - waiting for locator('[data-fs-field-errors]')

    at /Users/<USER>/Projects/project-controls/e2e/auth/password-reset.test.ts:88:56
```

# Page snapshot

```yaml
- region "Notifications alt+T"
- link "Cost Atlas":
  - /url: /
  - img
  - text: Cost Atlas
- heading "Change Password" [level=1]
- text: New Password *
- textbox "New Password *": short
- text: Password must be at least 8 characters Confirm Password *
- textbox "Confirm Password *": short
- text: Confirm password must be at least 8 characters
- button "Change Password"
```

# Test source

```ts
   1 | import { expect, test } from '@playwright/test';
   2 |
   3 | test.describe('Password Reset E2E Tests', () => {
   4 | 	const testEmail = `test-${Date.now()}@example.com`;
   5 |
   6 | 	test('should display password reset request form', async ({ page }) => {
   7 | 		// Navigate to the password reset page
   8 | 		await page.goto('/auth/reset-password');
   9 |
   10 | 		// Verify page title
   11 | 		await expect(page.locator('h1, h2').filter({ hasText: /reset\s+password/i })).toBeVisible();
   12 |
   13 | 		// Verify form elements
   14 | 		await expect(page.getByLabel(/email/i)).toBeVisible();
   15 | 		await expect(page.getByRole('button', { name: /reset/i })).toBeVisible();
   16 | 	});
   17 |
   18 | 	test('should validate email format', async ({ page }) => {
   19 | 		// Navigate to the password reset page
   20 | 		await page.goto('/auth/reset-password');
   21 |
   22 | 		// Enter invalid email
   23 | 		await page.getByLabel(/email/i).fill('invalid-email');
   24 |
   25 | 		// Submit the form
   26 | 		await page.getByRole('button', { name: /send reset link/i }).click();
   27 |
   28 | 		// Should show validation error - check for form field errors (be more specific)
   29 | 		await expect(page.locator('[data-fs-field-errors]')).toBeVisible({ timeout: 2000 });
   30 | 	});
   31 |
   32 | 	test('should submit reset request for valid email', async ({ page }) => {
   33 | 		// Navigate to the password reset page
   34 | 		await page.goto('/auth/reset-password');
   35 |
   36 | 		// Enter valid email
   37 | 		await page.getByLabel(/email/i).fill(testEmail);
   38 |
   39 | 		// Submit the form
   40 | 		await page.getByRole('button', { name: /send reset link/i }).click();
   41 |
   42 | 		// Should show success message via toast - wait for the toast to appear
   43 | 		await expect(page.locator('[data-sonner-toaster]')).toBeVisible({ timeout: 5000 });
   44 | 		await expect(page.locator('text=/check your email/i')).toBeVisible({ timeout: 5000 });
   45 | 	});
   46 |
   47 | 	// Note: Complete password reset flow cannot be fully automated without email integration
   48 | 	// The following test simulates the password reset confirmation page
   49 | 	test('should show password change form when using reset link', async ({ page }) => {
   50 | 		// Simulate navigating to the password change page with a reset token
   51 | 		// In a real scenario, this would come from clicking a link in an email
   52 | 		// For testing, we can directly navigate to the page with a mock token
   53 | 		await page.goto('/auth/change-password?token=mock-token');
   54 |
   55 | 		// Verify page has password fields
   56 | 		await expect(page.getByLabel(/password/i).first()).toBeVisible();
   57 | 		await expect(page.getByLabel(/confirm password/i)).toBeVisible();
   58 |
   59 | 		// Enter new password
   60 | 		await page
   61 | 			.getByLabel(/password/i)
   62 | 			.first()
   63 | 			.fill('NewPassword123');
   64 | 		await page.getByLabel(/confirm password/i).fill('NewPassword123');
   65 |
   66 | 		// Submit the form
   67 | 		await page
   68 | 			.getByRole('button', { name: /change password|update password|save password/i })
   69 | 			.click();
   70 |
   71 | 		// Since we're using a mock token, the form should be visible and functional
   72 | 		// We don't expect an error message immediately, just that the form is present
   73 | 		await expect(page.locator('h1').filter({ hasText: /change password/i })).toBeVisible();
   74 | 	});
   75 |
   76 | 	test('should validate password requirements', async ({ page }) => {
   77 | 		// Navigate to password change page
   78 | 		await page.goto('/auth/change-password?token=mock-token');
   79 |
   80 | 		// Enter too short password
   81 | 		await page.getByLabel(/new password/i).fill('short');
   82 | 		await page.getByLabel(/confirm password/i).fill('short');
   83 |
   84 | 		// Submit the form
   85 | 		await page.getByRole('button', { name: /change password/i }).click();
   86 |
   87 | 		// Should show password length validation error in form field errors
>  88 | 		await expect(page.locator('[data-fs-field-errors]')).toBeVisible({ timeout: 2000 });
      | 		                                                     ^ Error: expect.toBeVisible: Error: strict mode violation: locator('[data-fs-field-errors]') resolved to 2 elements:
   89 |
   90 | 		// Enter mismatched passwords
   91 | 		await page.getByLabel(/new password/i).fill('NewPassword123');
   92 | 		await page.getByLabel(/confirm password/i).fill('DifferentPassword123');
   93 |
   94 | 		// Submit the form
   95 | 		await page.getByRole('button', { name: /change password/i }).click();
   96 |
   97 | 		// Should show password mismatch validation error in form field errors
   98 | 		await expect(page.locator('[data-fs-field-errors]')).toBeVisible({ timeout: 2000 });
   99 | 	});
  100 | });
  101 |
```